/**
 * 视觉脚本实体节点
 * 提供实体操作相关的节点
 */
import { Entity } from '../../core/Entity';
import { FlowNode } from '../nodes/FlowNode';
import { NodeCategory, SocketDirection, SocketType, NodeOptions } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 组件类注册表
 * 用于根据组件类型名称获取组件类
 */
interface ComponentRegistry {
  [key: string]: any;
}

// 全局组件注册表
let componentRegistry: ComponentRegistry = {};

/**
 * 注册组件类
 * @param name 组件类型名称
 * @param componentClass 组件类
 */
export function registerComponent(name: string, componentClass: any): void {
  componentRegistry[name] = componentClass;
}

/**
 * 获取组件类
 * @param name 组件类型名称
 * @returns 组件类
 */
export function getComponentClass(name: string): any {
  return componentRegistry[name] || null;
}

/**
 * 实体管理器接口
 */
interface EntityManager {
  getEntity(id: string): Entity | null;
  getEntityByName(name: string): Entity | null;
  createEntity(name?: string): Entity;
  destroyEntity(entity: Entity): boolean;
}

// 全局实体管理器
let entityManager: EntityManager | null = null;

/**
 * 设置实体管理器
 * @param manager 实体管理器
 */
export function setEntityManager(manager: EntityManager): void {
  entityManager = manager;
}

/**
 * 获取实体节点
 * 根据ID获取实体
 */
export class GetEntityNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加实体ID输入
    this.addInput({
      name: 'entityId',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '实体ID',
      defaultValue: ''
    });

    // 添加实体输出
    this.addOutput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'entity',
      description: '实体'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    // 添加失败输出流程插槽
    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '失败时执行'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entityId = this.getInputValue('entityId') as string;

    // 检查实体管理器是否可用
    if (!entityManager) {
      console.error('实体管理器未设置');
      this.triggerFlow('fail');
      return null;
    }

    // 获取实体
    const entity = entityManager.getEntity(entityId);

    // 检查实体是否存在
    if (entity) {
      // 设置输出值
      this.setOutputValue('entity', entity);

      // 触发输出流程
      this.triggerFlow('flow');

      return entity;
    } else {
      // 触发失败流程
      this.triggerFlow('fail');

      return null;
    }
  }
}

/**
 * 创建实体节点
 * 创建一个新的实体
 */
export class CreateEntityNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加实体名称输入
    this.addInput({
      name: 'name',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '实体名称',
      defaultValue: '新实体'
    });

    // 添加实体输出
    this.addOutput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'entity',
      description: '创建的实体'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    // 添加失败输出流程插槽
    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '失败时执行'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const name = this.getInputValue('name') as string;

    // 检查实体管理器是否可用
    if (!entityManager) {
      console.error('实体管理器未设置');
      this.triggerFlow('fail');
      return null;
    }

    try {
      // 创建实体
      const entity = entityManager.createEntity(name);

      // 设置输出值
      this.setOutputValue('entity', entity);

      // 触发输出流程
      this.triggerFlow('flow');

      return entity;
    } catch (error) {
      console.error('创建实体失败:', error);

      // 触发失败流程
      this.triggerFlow('fail');

      return null;
    }
  }
}

/**
 * 销毁实体节点
 * 销毁指定的实体
 */
export class DestroyEntityNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'entity',
      description: '要销毁的实体'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    // 添加失败输出流程插槽
    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '失败时执行'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;

    // 检查实体是否有效
    if (!entity) {
      this.triggerFlow('fail');
      return false;
    }

    // 检查实体管理器是否可用
    if (!entityManager) {
      console.error('实体管理器未设置');
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 销毁实体
      const result = entityManager.destroyEntity(entity);

      if (result) {
        // 触发输出流程
        this.triggerFlow('flow');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('销毁实体失败:', error);

      // 触发失败流程
      this.triggerFlow('fail');

      return false;
    }
  }
}

/**
 * 获取组件节点
 * 获取实体上的组件
 */
export class GetComponentNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'entity',
      description: '实体'
    });

    // 添加组件类型输入
    this.addInput({
      name: 'componentType',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '组件类型',
      defaultValue: ''
    });

    // 添加组件输出
    this.addOutput({
      name: 'component',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'component',
      description: '组件'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    // 添加失败输出流程插槽
    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '失败时执行'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const componentType = this.getInputValue('componentType') as string;

    // 检查实体和组件类型是否有效
    if (!entity || !componentType) {
      // 触发失败流程
      this.triggerFlow('fail');
      return null;
    }

    // 获取组件类
    const ComponentClass = getComponentClass(componentType);

    // 检查组件类是否存在
    if (!ComponentClass) {
      // 触发失败流程
      this.triggerFlow('fail');
      return null;
    }

    // 检查实体是否有该组件
    if (entity.hasComponent(componentType)) {
      // 获取组件
      const component = entity.getComponent(componentType);

      // 设置输出值
      this.setOutputValue('component', component);

      // 触发输出流程
      this.triggerFlow('flow');

      return component;
    } else {
      // 触发失败流程
      this.triggerFlow('fail');

      return null;
    }
  }


}

/**
 * 添加组件节点
 * 向实体添加组件
 */
export class AddComponentNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'entity',
      description: '实体'
    });

    // 添加组件类型输入
    this.addInput({
      name: 'componentType',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '组件类型',
      defaultValue: ''
    });

    // 添加组件数据输入
    this.addInput({
      name: 'data',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '组件数据',
      defaultValue: {}
    });

    // 添加组件输出
    this.addOutput({
      name: 'component',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'component',
      description: '组件'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    // 添加失败输出流程插槽
    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '失败时执行'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const componentType = this.getInputValue('componentType') as string;
    const data = this.getInputValue('data') as object;

    // 检查实体和组件类型是否有效
    if (!entity || !componentType) {
      // 触发失败流程
      this.triggerFlow('fail');
      return null;
    }

    // 获取组件类
    const ComponentClass = getComponentClass(componentType);

    // 检查组件类是否存在
    if (!ComponentClass) {
      // 触发失败流程
      this.triggerFlow('fail');
      return null;
    }

    try {
      // 创建组件实例
      const component = new ComponentClass(data);
      // 添加组件到实体
      entity.addComponent(component);

      // 设置输出值
      this.setOutputValue('component', component);

      // 触发输出流程
      this.triggerFlow('flow');

      return component;
    } catch (error) {
      console.error('添加组件失败:', error);

      // 触发失败流程
      this.triggerFlow('fail');

      return null;
    }
  }
}

/**
 * 检查组件节点
 * 检查实体是否有指定组件
 */
export class HasComponentNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super({
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['true', 'false']
    });
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      displayName: '实体',
      description: '要检查的实体'
    });

    this.addInput({
      name: 'componentType',
      type: SocketType.DATA,
      dataType: 'string',
      displayName: '组件类型',
      description: '要检查的组件类型'
    });

    // 输出插槽
    this.addOutput({
      name: 'hasComponent',
      type: SocketType.DATA,
      dataType: 'boolean',
      displayName: '有组件',
      description: '是否有该组件'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const componentType = this.getInputValue('componentType') as string;

    // 检查实体和组件类型是否有效
    if (!entity || !componentType) {
      // 设置输出值为false
      this.setOutputValue('hasComponent', false);
      // 触发false流程
      this.triggerFlow('false');
      return false;
    }

    // 检查实体是否有该组件
    const hasComponent = entity.hasComponent(componentType);

    // 设置输出值
    this.setOutputValue('hasComponent', hasComponent);

    // 触发对应的流程
    if (hasComponent) {
      this.triggerFlow('true');
    } else {
      this.triggerFlow('false');
    }

    return hasComponent;
  }
}

/**
 * 移除组件节点
 * 从实体移除组件
 */
export class RemoveComponentNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'entity',
      description: '实体'
    });

    // 添加组件类型输入
    this.addInput({
      name: 'componentType',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '组件类型',
      defaultValue: ''
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    // 添加失败输出流程插槽
    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '失败时执行'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const componentType = this.getInputValue('componentType') as string;

    // 检查实体和组件类型是否有效
    if (!entity || !componentType) {
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }

    // 获取组件类
    const ComponentClass = getComponentClass(componentType);

    // 检查组件类是否存在
    if (!ComponentClass) {
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 检查实体是否有该组件
      if (entity.hasComponent(componentType)) {
        // 移除组件
        entity.removeComponent(componentType);

        // 触发输出流程
        this.triggerFlow('flow');

        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');

        return false;
      }
    } catch (error) {
      console.error('移除组件失败:', error);

      // 触发失败流程
      this.triggerFlow('fail');

      return false;
    }
  }
}



/**
 * 获取实体名称节点
 * 获取实体的名称
 */
export class GetEntityNameNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'entity',
      description: '实体'
    });

    // 添加名称输出
    this.addOutput({
      name: 'name',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '实体名称'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    // 添加失败输出流程插槽
    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '失败时执行'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;

    // 检查实体是否有效
    if (!entity) {
      this.triggerFlow('fail');
      return null;
    }

    // 获取实体名称
    const name = entity.name;

    // 设置输出值
    this.setOutputValue('name', name);

    // 触发输出流程
    this.triggerFlow('flow');

    return name;
  }
}

/**
 * 设置实体名称节点
 * 设置实体的名称
 */
export class SetEntityNameNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'entity',
      description: '实体'
    });

    // 添加名称输入
    this.addInput({
      name: 'name',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '新名称',
      defaultValue: ''
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    // 添加失败输出流程插槽
    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '失败时执行'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const name = this.getInputValue('name') as string;

    // 检查实体是否有效
    if (!entity) {
      this.triggerFlow('fail');
      return false;
    }

    // 检查名称是否有效
    if (typeof name !== 'string') {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 设置实体名称
      entity.name = name;

      // 触发输出流程
      this.triggerFlow('flow');

      return true;
    } catch (error) {
      console.error('设置实体名称失败:', error);

      // 触发失败流程
      this.triggerFlow('fail');

      return false;
    }
  }
}

/**
 * 获取实体标签节点
 * 获取实体的标签
 */
export class GetEntityTagNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'entity',
      description: '实体'
    });

    // 添加标签输出
    this.addOutput({
      name: 'tags',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'array',
      description: '实体标签列表'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    // 添加失败输出流程插槽
    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '失败时执行'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;

    // 检查实体是否有效
    if (!entity) {
      this.triggerFlow('fail');
      return null;
    }

    try {
      // 获取实体标签
      const tags = entity.getTags();

      // 设置输出值
      this.setOutputValue('tags', tags);

      // 触发输出流程
      this.triggerFlow('flow');

      return tags;
    } catch (error) {
      console.error('获取实体标签失败:', error);

      // 触发失败流程
      this.triggerFlow('fail');

      return null;
    }
  }
}

/**
 * 设置实体标签节点
 * 设置实体的标签
 */
export class SetEntityTagNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'entity',
      description: '实体'
    });

    // 添加标签输入
    this.addInput({
      name: 'tag',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '标签',
      defaultValue: ''
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    // 添加失败输出流程插槽
    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '失败时执行'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const tag = this.getInputValue('tag') as string;

    // 检查实体是否有效
    if (!entity) {
      this.triggerFlow('fail');
      return false;
    }

    // 检查标签是否有效
    if (typeof tag !== 'string' || tag.trim() === '') {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 添加标签到实体
      entity.addTag(tag);

      // 触发输出流程
      this.triggerFlow('flow');

      return true;
    } catch (error) {
      console.error('设置实体标签失败:', error);

      // 触发失败流程
      this.triggerFlow('fail');

      return false;
    }
  }
}

/**
 * 实体激活状态节点
 * 检查实体是否处于激活状态
 */
export class IsEntityActiveNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'entity',
      description: '实体'
    });

    // 添加激活状态输出
    this.addOutput({
      name: 'isActive',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否激活'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    // 添加失败输出流程插槽
    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '失败时执行'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;

    // 检查实体是否有效
    if (!entity) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取实体激活状态
      const isActive = entity.isActive();

      // 设置输出值
      this.setOutputValue('isActive', isActive);

      // 触发输出流程
      this.triggerFlow('flow');

      return isActive;
    } catch (error) {
      console.error('获取实体激活状态失败:', error);

      // 触发失败流程
      this.triggerFlow('fail');

      return false;
    }
  }
}

/**
 * 设置激活状态节点
 * 设置实体的激活状态
 */
export class SetEntityActiveNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'entity',
      description: '实体'
    });

    // 添加激活状态输入
    this.addInput({
      name: 'active',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '激活状态',
      defaultValue: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    // 添加失败输出流程插槽
    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '失败时执行'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const active = this.getInputValue('active') as boolean;

    // 检查实体是否有效
    if (!entity) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 设置实体激活状态
      entity.setActive(active);

      // 触发输出流程
      this.triggerFlow('flow');

      return true;
    } catch (error) {
      console.error('设置实体激活状态失败:', error);

      // 触发失败流程
      this.triggerFlow('fail');

      return false;
    }
  }
}

/**
 * 获取父实体节点
 * 获取实体的父实体
 */
export class GetEntityParentNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'entity',
      description: '实体'
    });

    // 添加父实体输出
    this.addOutput({
      name: 'parent',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'entity',
      description: '父实体'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    // 添加失败输出流程插槽
    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '失败时执行'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;

    // 检查实体是否有效
    if (!entity) {
      this.triggerFlow('fail');
      return null;
    }

    try {
      // 获取父实体
      const parent = entity.getParent();

      // 设置输出值
      this.setOutputValue('parent', parent);

      // 触发输出流程
      this.triggerFlow('flow');

      return parent;
    } catch (error) {
      console.error('获取父实体失败:', error);

      // 触发失败流程
      this.triggerFlow('fail');

      return null;
    }
  }
}

/**
 * 设置父实体节点
 * 设置实体的父实体
 */
export class SetEntityParentNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'entity',
      description: '子实体'
    });

    // 添加父实体输入
    this.addInput({
      name: 'parent',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'entity',
      description: '父实体'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    // 添加失败输出流程插槽
    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '失败时执行'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const parent = this.getInputValue('parent') as Entity;

    // 检查实体是否有效
    if (!entity) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 设置父实体
      if (parent) {
        parent.addChild(entity);
      } else {
        // 如果父实体为null，则移除父子关系
        const currentParent = entity.getParent();
        if (currentParent) {
          currentParent.removeChild(entity);
        }
      }

      // 触发输出流程
      this.triggerFlow('flow');

      return true;
    } catch (error) {
      console.error('设置父实体失败:', error);

      // 触发失败流程
      this.triggerFlow('fail');

      return false;
    }
  }
}

/**
 * 获取子实体节点
 * 获取实体的所有子实体
 */
export class GetEntityChildrenNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'entity',
      description: '实体'
    });

    // 添加子实体列表输出
    this.addOutput({
      name: 'children',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'array',
      description: '子实体列表'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    // 添加失败输出流程插槽
    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '失败时执行'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;

    // 检查实体是否有效
    if (!entity) {
      this.triggerFlow('fail');
      return null;
    }

    try {
      // 获取子实体列表
      const children = entity.getChildren();

      // 设置输出值
      this.setOutputValue('children', children);

      // 触发输出流程
      this.triggerFlow('flow');

      return children;
    } catch (error) {
      console.error('获取子实体失败:', error);

      // 触发失败流程
      this.triggerFlow('fail');

      return null;
    }
  }
}

/**
 * 注册实体节点
 * @param registry 节点注册表
 */
export function registerEntityNodes(registry: NodeRegistry): void {
  // 注册创建实体节点
  registry.registerNodeType({
    type: 'entity/create',
    category: NodeCategory.ENTITY,
    constructor: CreateEntityNode,
    label: '创建实体',
    description: '创建一个新的实体',
    icon: 'entity',
    color: '#4CAF50',
    tags: ['entity', 'create']
  });

  // 注册销毁实体节点
  registry.registerNodeType({
    type: 'entity/destroy',
    category: NodeCategory.ENTITY,
    constructor: DestroyEntityNode,
    label: '销毁实体',
    description: '销毁指定的实体',
    icon: 'entity',
    color: '#F44336',
    tags: ['entity', 'destroy']
  });

  // 注册获取实体节点
  registry.registerNodeType({
    type: 'entity/get',
    category: NodeCategory.ENTITY,
    constructor: GetEntityNode,
    label: '获取实体',
    description: '根据ID或名称获取实体',
    icon: 'entity',
    color: '#4CAF50',
    tags: ['entity', 'get']
  });

  // 注册获取实体名称节点
  registry.registerNodeType({
    type: 'entity/getName',
    category: NodeCategory.ENTITY,
    constructor: GetEntityNameNode,
    label: '获取实体名称',
    description: '获取实体的名称',
    icon: 'text',
    color: '#2196F3',
    tags: ['entity', 'name', 'get']
  });

  // 注册设置实体名称节点
  registry.registerNodeType({
    type: 'entity/setName',
    category: NodeCategory.ENTITY,
    constructor: SetEntityNameNode,
    label: '设置实体名称',
    description: '设置实体的名称',
    icon: 'text',
    color: '#2196F3',
    tags: ['entity', 'name', 'set']
  });

  // 注册获取实体标签节点
  registry.registerNodeType({
    type: 'entity/getTag',
    category: NodeCategory.ENTITY,
    constructor: GetEntityTagNode,
    label: '获取实体标签',
    description: '获取实体的标签',
    icon: 'tag',
    color: '#FF9800',
    tags: ['entity', 'tag', 'get']
  });

  // 注册设置实体标签节点
  registry.registerNodeType({
    type: 'entity/setTag',
    category: NodeCategory.ENTITY,
    constructor: SetEntityTagNode,
    label: '设置实体标签',
    description: '设置实体的标签',
    icon: 'tag',
    color: '#FF9800',
    tags: ['entity', 'tag', 'set']
  });

  // 注册实体激活状态节点
  registry.registerNodeType({
    type: 'entity/isActive',
    category: NodeCategory.ENTITY,
    constructor: IsEntityActiveNode,
    label: '实体激活状态',
    description: '检查实体是否处于激活状态',
    icon: 'visibility',
    color: '#9C27B0',
    tags: ['entity', 'active', 'state']
  });

  // 注册设置激活状态节点
  registry.registerNodeType({
    type: 'entity/setActive',
    category: NodeCategory.ENTITY,
    constructor: SetEntityActiveNode,
    label: '设置激活状态',
    description: '设置实体的激活状态',
    icon: 'visibility',
    color: '#9C27B0',
    tags: ['entity', 'active', 'set']
  });

  // 注册获取父实体节点
  registry.registerNodeType({
    type: 'entity/getParent',
    category: NodeCategory.ENTITY,
    constructor: GetEntityParentNode,
    label: '获取父实体',
    description: '获取实体的父实体',
    icon: 'hierarchy',
    color: '#607D8B',
    tags: ['entity', 'parent', 'hierarchy']
  });

  // 注册设置父实体节点
  registry.registerNodeType({
    type: 'entity/setParent',
    category: NodeCategory.ENTITY,
    constructor: SetEntityParentNode,
    label: '设置父实体',
    description: '设置实体的父实体',
    icon: 'hierarchy',
    color: '#607D8B',
    tags: ['entity', 'parent', 'hierarchy']
  });

  // 注册获取子实体节点
  registry.registerNodeType({
    type: 'entity/getChildren',
    category: NodeCategory.ENTITY,
    constructor: GetEntityChildrenNode,
    label: '获取子实体',
    description: '获取实体的所有子实体',
    icon: 'hierarchy',
    color: '#607D8B',
    tags: ['entity', 'children', 'hierarchy']
  });

  // 注册添加组件节点
  registry.registerNodeType({
    type: 'component/add',
    category: NodeCategory.ENTITY,
    constructor: AddComponentNode,
    label: '添加组件',
    description: '向实体添加指定类型的组件',
    icon: 'addComponent',
    color: '#4CAF50',
    tags: ['component', 'add']
  });

  // 注册移除组件节点
  registry.registerNodeType({
    type: 'component/remove',
    category: NodeCategory.ENTITY,
    constructor: RemoveComponentNode,
    label: '移除组件',
    description: '从实体移除指定类型的组件',
    icon: 'removeComponent',
    color: '#F44336',
    tags: ['component', 'remove']
  });

  // 注册获取组件节点
  registry.registerNodeType({
    type: 'component/get',
    category: NodeCategory.ENTITY,
    constructor: GetComponentNode,
    label: '获取组件',
    description: '获取实体上的指定组件',
    icon: 'component',
    color: '#4CAF50',
    tags: ['component', 'get']
  });

  // 注册检查组件节点
  registry.registerNodeType({
    type: 'component/hasComponent',
    category: NodeCategory.ENTITY,
    constructor: HasComponentNode,
    label: '组件检查',
    description: '检查实体是否有指定组件',
    icon: 'search',
    color: '#2196F3',
    tags: ['component', 'check', 'has']
  });
}
