/**
 * 变换和场景节点测试
 */
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { NodeRegistry } from '../../src/visualscript/nodes/NodeRegistry';
import { Entity } from '../../src/core/Entity';
import { World } from '../../src/core/World';
import { Engine } from '../../src/core/Engine';
import { Transform } from '../../src/scene/Transform';
import { Scene } from '../../src/scene/Scene';
import * as THREE from 'three';

// 导入节点
import {
  HasComponentNode,
  GetPositionNode,
  SetPositionNode,
  GetRotationNode,
  SetRotationNode,
  GetScaleNode,
  SetScaleNode,
  TranslateNode,
  RotateNode,
  LookAtNode,
  LoadSceneNode,
  GetCurrentSceneNode,
  FindEntityNode,
  FindEntitiesByTagNode,
  InstantiateNode,
  registerTransformNodes,
  registerSceneNodes
} from '../../src/visualscript/presets';

describe('变换和场景节点测试', () => {
  let registry: NodeRegistry;
  let engine: Engine;
  let world: World;
  let entity: Entity;
  let transform: Transform;

  beforeEach(() => {
    registry = new NodeRegistry();
    engine = new Engine({ autoStart: false });
    world = engine.getWorld();
    entity = world.createEntity('TestEntity');
    transform = entity.getComponent<Transform>('Transform')!;

    // 注册节点
    registerTransformNodes(registry);
    registerSceneNodes(registry);
  });

  afterEach(() => {
    if (engine) {
      engine.dispose();
    }
  });

  describe('组件检查节点', () => {
    it('应该能够检查实体是否有组件', () => {
      const nodeInfo = registry.getNodeTypeInfo('component/hasComponent');
      expect(nodeInfo).toBeDefined();
      expect(nodeInfo?.label).toBe('组件检查');
    });
  });

  describe('变换节点', () => {
    it('应该注册所有变换节点', () => {
      const transformNodeTypes = [
        'transform/getPosition',
        'transform/setPosition',
        'transform/getRotation',
        'transform/setRotation',
        'transform/getScale',
        'transform/setScale',
        'transform/translate',
        'transform/rotate',
        'transform/lookAt'
      ];

      for (const nodeType of transformNodeTypes) {
        const nodeInfo = registry.getNodeTypeInfo(nodeType);
        expect(nodeInfo).toBeDefined();
        console.log(`✓ ${nodeType}: ${nodeInfo?.label}`);
      }
    });

    it('应该能够获取和设置位置', () => {
      // 设置初始位置
      const initialPosition = new THREE.Vector3(1, 2, 3);
      transform.setPosition(initialPosition);

      // 获取节点构造函数
      const NodeConstructor = registry.getNodeType('transform/getPosition');
      expect(NodeConstructor).toBeDefined();

      if (NodeConstructor) {
        // 创建节点实例
        const getPositionNode = new NodeConstructor({
          id: 'test-get-position',
          type: 'transform/getPosition',
          graph: null,
          context: null
        });

        // 设置输入
        getPositionNode.setInputValue('entity', entity);

        // 执行节点
        const result = getPositionNode.execute();

        // 验证结果
        expect(result).toBeDefined();
        expect(result.x).toBeCloseTo(1);
        expect(result.y).toBeCloseTo(2);
        expect(result.z).toBeCloseTo(3);
      }
    });

    it('应该能够平移实体', () => {
      // 设置初始位置
      const initialPosition = new THREE.Vector3(0, 0, 0);
      transform.setPosition(initialPosition);

      // 创建平移节点
      const translateNode = registry.createNode('transform/translate', {
        id: 'test-translate',
        type: 'transform/translate',
        graph: null,
        context: null
      });

      expect(translateNode).toBeDefined();
      
      if (translateNode) {
        // 设置输入
        translateNode.setInputValue('entity', entity);
        translateNode.setInputValue('translation', new THREE.Vector3(1, 1, 1));
        
        // 执行节点
        translateNode.execute();
        
        // 验证结果
        const newPosition = transform.getPosition();
        expect(newPosition.x).toBeCloseTo(1);
        expect(newPosition.y).toBeCloseTo(1);
        expect(newPosition.z).toBeCloseTo(1);
      }
    });
  });

  describe('场景节点', () => {
    it('应该注册所有场景节点', () => {
      const sceneNodeTypes = [
        'scene/loadScene',
        'scene/getCurrentScene',
        'scene/findEntity',
        'scene/findEntitiesByTag',
        'scene/instantiate'
      ];

      for (const nodeType of sceneNodeTypes) {
        const nodeInfo = registry.getNodeTypeInfo(nodeType);
        expect(nodeInfo).toBeDefined();
        console.log(`✓ ${nodeType}: ${nodeInfo?.label}`);
      }
    });

    it('应该能够加载场景', () => {
      // 创建加载场景节点
      const loadSceneNode = registry.createNode('scene/loadScene', {
        id: 'test-load-scene',
        type: 'scene/loadScene',
        graph: null,
        context: null
      });

      expect(loadSceneNode).toBeDefined();
      
      if (loadSceneNode) {
        // 设置输入
        loadSceneNode.setInputValue('sceneName', 'TestScene');
        
        // 执行节点
        const result = loadSceneNode.execute();
        
        // 验证结果
        expect(result).toBeDefined();
        expect(result.name).toBe('TestScene');
      }
    });

    it('应该能够实例化预制体', () => {
      // 创建实例化节点
      const instantiateNode = registry.createNode('scene/instantiate', {
        id: 'test-instantiate',
        type: 'scene/instantiate',
        graph: null,
        context: null
      });

      expect(instantiateNode).toBeDefined();
      
      if (instantiateNode) {
        // 设置输入
        instantiateNode.setInputValue('prefab', 'TestPrefab');
        instantiateNode.setInputValue('position', new THREE.Vector3(5, 5, 5));
        
        // 执行节点
        const result = instantiateNode.execute();
        
        // 验证结果
        expect(result).toBeDefined();
        expect(result.name).toContain('TestPrefab');
      }
    });
  });

  describe('节点搜索', () => {
    it('应该能够搜索变换相关节点', () => {
      const searchResults = registry.searchNodeTypes('变换');
      expect(searchResults.length).toBeGreaterThan(0);
      
      // 验证搜索结果包含变换节点
      const transformNodeFound = searchResults.some(node => node.type.includes('transform'));
      expect(transformNodeFound).toBe(true);
    });

    it('应该能够搜索场景相关节点', () => {
      const searchResults = registry.searchNodeTypes('场景');
      expect(searchResults.length).toBeGreaterThan(0);
      
      // 验证搜索结果包含场景节点
      const sceneNodeFound = searchResults.some(node => node.type.includes('scene'));
      expect(sceneNodeFound).toBe(true);
    });

    it('应该能够搜索组件相关节点', () => {
      const searchResults = registry.searchNodeTypes('组件');
      expect(searchResults.length).toBeGreaterThan(0);
      
      // 验证搜索结果包含组件节点
      const componentNodeFound = searchResults.some(node => node.type.includes('component'));
      expect(componentNodeFound).toBe(true);
    });
  });

  describe('节点分类', () => {
    it('应该将节点正确分类', () => {
      const allNodes = registry.getAllNodeTypes();
      
      // 检查变换节点分类
      const transformNodes = allNodes.filter(node => node.type.startsWith('transform/'));
      transformNodes.forEach(node => {
        expect(node.tags).toContain('transform');
      });

      // 检查场景节点分类
      const sceneNodes = allNodes.filter(node => node.type.startsWith('scene/'));
      sceneNodes.forEach(node => {
        expect(node.tags).toContain('scene');
      });

      // 检查组件节点分类
      const componentNodes = allNodes.filter(node => node.type.startsWith('component/'));
      componentNodes.forEach(node => {
        expect(node.tags).toContain('component');
      });
    });
  });
});
