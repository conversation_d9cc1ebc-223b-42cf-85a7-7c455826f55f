/**
 * 变换操作节点
 * 提供实体变换相关的可视化脚本节点
 */

import { FlowNode, FlowNodeOptions } from '../nodes/FlowNode';
import { NodeOptions, SocketType, NodeCategory } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { Entity } from '../../core/Entity';
import { Transform } from '../../scene/Transform';
import * as THREE from 'three';

/**
 * 获取位置节点
 * 获取实体的世界位置
 */
export class GetPositionNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['flow']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      displayName: '实体',
      description: '要获取位置的实体'
    });

    // 输出插槽
    this.addOutput({
      name: 'position',
      type: SocketType.DATA,
      dataType: 'Vector3',
      displayName: '位置',
      description: '实体的世界位置'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;

    // 检查实体是否有效
    if (!entity) {
      console.warn('GetPositionNode: 实体无效');
      this.triggerFlow('flow');
      return null;
    }

    // 获取变换组件
    const transform = entity.getComponent<Transform>('Transform');
    if (!transform) {
      console.warn('GetPositionNode: 实体没有变换组件');
      this.triggerFlow('flow');
      return null;
    }

    // 获取世界位置
    const position = transform.getWorldPosition();

    // 设置输出值
    this.setOutputValue('position', position);

    // 触发输出流程
    this.triggerFlow('flow');

    return position;
  }
}

/**
 * 设置位置节点
 * 设置实体的世界位置
 */
export class SetPositionNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['flow']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      displayName: '实体',
      description: '要设置位置的实体'
    });

    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      dataType: 'Vector3',
      displayName: '位置',
      description: '新的世界位置',
      defaultValue: new THREE.Vector3(0, 0, 0)
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const position = this.getInputValue('position') as THREE.Vector3;

    // 检查实体是否有效
    if (!entity) {
      console.warn('SetPositionNode: 实体无效');
      this.triggerFlow('flow');
      return;
    }

    // 获取变换组件
    const transform = entity.getComponent<Transform>('Transform');
    if (!transform) {
      console.warn('SetPositionNode: 实体没有变换组件');
      this.triggerFlow('flow');
      return;
    }

    // 设置位置
    if (position) {
      transform.setPosition(position);
    }

    // 触发输出流程
    this.triggerFlow('flow');
  }
}

/**
 * 获取旋转节点
 * 获取实体的旋转角度
 */
export class GetRotationNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['flow']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      displayName: '实体',
      description: '要获取旋转的实体'
    });

    // 输出插槽
    this.addOutput({
      name: 'rotation',
      type: SocketType.DATA,
      dataType: 'Euler',
      displayName: '旋转',
      description: '实体的旋转角度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;

    // 检查实体是否有效
    if (!entity) {
      console.warn('GetRotationNode: 实体无效');
      this.triggerFlow('flow');
      return null;
    }

    // 获取变换组件
    const transform = entity.getComponent<Transform>('Transform');
    if (!transform) {
      console.warn('GetRotationNode: 实体没有变换组件');
      this.triggerFlow('flow');
      return null;
    }

    // 获取旋转
    const rotation = transform.getRotation();

    // 设置输出值
    this.setOutputValue('rotation', rotation);

    // 触发输出流程
    this.triggerFlow('flow');

    return rotation;
  }
}

/**
 * 设置旋转节点
 * 设置实体的旋转角度
 */
export class SetRotationNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['flow']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      displayName: '实体',
      description: '要设置旋转的实体'
    });

    this.addInput({
      name: 'rotation',
      type: SocketType.DATA,
      dataType: 'Euler',
      displayName: '旋转',
      description: '新的旋转角度',
      defaultValue: new THREE.Euler(0, 0, 0)
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const rotation = this.getInputValue('rotation') as THREE.Euler;

    // 检查实体是否有效
    if (!entity) {
      console.warn('SetRotationNode: 实体无效');
      this.triggerFlow('flow');
      return;
    }

    // 获取变换组件
    const transform = entity.getComponent<Transform>('Transform');
    if (!transform) {
      console.warn('SetRotationNode: 实体没有变换组件');
      this.triggerFlow('flow');
      return;
    }

    // 设置旋转
    if (rotation) {
      transform.setRotation(rotation);
    }

    // 触发输出流程
    this.triggerFlow('flow');
  }
}

/**
 * 获取缩放节点
 * 获取实体的缩放比例
 */
export class GetScaleNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['flow']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      displayName: '实体',
      description: '要获取缩放的实体'
    });

    // 输出插槽
    this.addOutput({
      name: 'scale',
      type: SocketType.DATA,
      dataType: 'Vector3',
      displayName: '缩放',
      description: '实体的缩放比例'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;

    // 检查实体是否有效
    if (!entity) {
      console.warn('GetScaleNode: 实体无效');
      this.triggerFlow('flow');
      return null;
    }

    // 获取变换组件
    const transform = entity.getComponent<Transform>('Transform');
    if (!transform) {
      console.warn('GetScaleNode: 实体没有变换组件');
      this.triggerFlow('flow');
      return null;
    }

    // 获取缩放
    const scale = transform.getScale();

    // 设置输出值
    this.setOutputValue('scale', scale);

    // 触发输出流程
    this.triggerFlow('flow');

    return scale;
  }
}

/**
 * 设置缩放节点
 * 设置实体的缩放比例
 */
export class SetScaleNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['flow']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      displayName: '实体',
      description: '要设置缩放的实体'
    });

    this.addInput({
      name: 'scale',
      type: SocketType.DATA,
      dataType: 'Vector3',
      displayName: '缩放',
      description: '新的缩放比例',
      defaultValue: new THREE.Vector3(1, 1, 1)
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const scale = this.getInputValue('scale') as THREE.Vector3;

    // 检查实体是否有效
    if (!entity) {
      console.warn('SetScaleNode: 实体无效');
      this.triggerFlow('flow');
      return;
    }

    // 获取变换组件
    const transform = entity.getComponent<Transform>('Transform');
    if (!transform) {
      console.warn('SetScaleNode: 实体没有变换组件');
      this.triggerFlow('flow');
      return;
    }

    // 设置缩放
    if (scale) {
      transform.setScale(scale);
    }

    // 触发输出流程
    this.triggerFlow('flow');
  }
}

/**
 * 平移节点
 * 平移实体位置
 */
export class TranslateNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['flow']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      displayName: '实体',
      description: '要平移的实体'
    });

    this.addInput({
      name: 'translation',
      type: SocketType.DATA,
      dataType: 'Vector3',
      displayName: '平移量',
      description: '平移的向量',
      defaultValue: new THREE.Vector3(0, 0, 0)
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const translation = this.getInputValue('translation') as THREE.Vector3;

    // 检查实体是否有效
    if (!entity) {
      console.warn('TranslateNode: 实体无效');
      this.triggerFlow('flow');
      return;
    }

    // 获取变换组件
    const transform = entity.getComponent<Transform>('Transform');
    if (!transform) {
      console.warn('TranslateNode: 实体没有变换组件');
      this.triggerFlow('flow');
      return;
    }

    // 平移
    if (translation) {
      const currentPosition = transform.getPosition();
      const newPosition = currentPosition.clone().add(translation);
      transform.setPosition(newPosition);
    }

    // 触发输出流程
    this.triggerFlow('flow');
  }
}

/**
 * 旋转节点
 * 旋转实体
 */
export class RotateNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['flow']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      displayName: '实体',
      description: '要旋转的实体'
    });

    this.addInput({
      name: 'rotation',
      type: SocketType.DATA,
      dataType: 'Euler',
      displayName: '旋转量',
      description: '旋转的角度',
      defaultValue: new THREE.Euler(0, 0, 0)
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const rotation = this.getInputValue('rotation') as THREE.Euler;

    // 检查实体是否有效
    if (!entity) {
      console.warn('RotateNode: 实体无效');
      this.triggerFlow('flow');
      return;
    }

    // 获取变换组件
    const transform = entity.getComponent<Transform>('Transform');
    if (!transform) {
      console.warn('RotateNode: 实体没有变换组件');
      this.triggerFlow('flow');
      return;
    }

    // 旋转
    if (rotation) {
      const currentRotation = transform.getRotation();
      const newRotation = new THREE.Euler(
        currentRotation.x + rotation.x,
        currentRotation.y + rotation.y,
        currentRotation.z + rotation.z,
        currentRotation.order
      );
      transform.setRotation(newRotation);
    }

    // 触发输出流程
    this.triggerFlow('flow');
  }
}

/**
 * 朝向目标节点
 * 让实体朝向指定目标
 */
export class LookAtNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['flow']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      displayName: '实体',
      description: '要旋转的实体'
    });

    this.addInput({
      name: 'target',
      type: SocketType.DATA,
      dataType: 'Vector3',
      displayName: '目标位置',
      description: '要朝向的目标位置',
      defaultValue: new THREE.Vector3(0, 0, 0)
    });

    this.addInput({
      name: 'up',
      type: SocketType.DATA,
      dataType: 'Vector3',
      displayName: '上方向',
      description: '上方向向量（可选）',
      defaultValue: new THREE.Vector3(0, 1, 0)
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const target = this.getInputValue('target') as THREE.Vector3;
    const up = this.getInputValue('up') as THREE.Vector3;

    // 检查实体是否有效
    if (!entity) {
      console.warn('LookAtNode: 实体无效');
      this.triggerFlow('flow');
      return;
    }

    // 获取变换组件
    const transform = entity.getComponent<Transform>('Transform');
    if (!transform) {
      console.warn('LookAtNode: 实体没有变换组件');
      this.triggerFlow('flow');
      return;
    }

    // 朝向目标
    if (target) {
      // 获取当前位置
      const currentPosition = transform.getPosition();

      // 计算朝向方向
      const direction = target.clone().sub(currentPosition).normalize();

      // 创建朝向矩阵
      const matrix = new THREE.Matrix4();
      matrix.lookAt(currentPosition, target, up || new THREE.Vector3(0, 1, 0));

      // 从矩阵提取旋转
      const rotation = new THREE.Euler();
      rotation.setFromRotationMatrix(matrix);

      // 设置旋转
      transform.setRotation(rotation);
    }

    // 触发输出流程
    this.triggerFlow('flow');
  }
}

/**
 * 注册变换节点
 * @param registry 节点注册表
 */
export function registerTransformNodes(registry: NodeRegistry): void {
  // 注册获取位置节点
  registry.registerNodeType({
    type: 'transform/getPosition',
    category: NodeCategory.ENTITY,
    constructor: GetPositionNode,
    label: '获取位置',
    description: '获取实体的世界位置',
    icon: 'position',
    color: '#FF9800',
    tags: ['transform', 'position', 'get']
  });

  // 注册设置位置节点
  registry.registerNodeType({
    type: 'transform/setPosition',
    category: NodeCategory.ENTITY,
    constructor: SetPositionNode,
    label: '设置位置',
    description: '设置实体的世界位置',
    icon: 'position',
    color: '#FF9800',
    tags: ['transform', 'position', 'set']
  });

  // 注册获取旋转节点
  registry.registerNodeType({
    type: 'transform/getRotation',
    category: NodeCategory.ENTITY,
    constructor: GetRotationNode,
    label: '获取旋转',
    description: '获取实体的旋转角度',
    icon: 'rotation',
    color: '#FF9800',
    tags: ['transform', 'rotation', 'get']
  });

  // 注册设置旋转节点
  registry.registerNodeType({
    type: 'transform/setRotation',
    category: NodeCategory.ENTITY,
    constructor: SetRotationNode,
    label: '设置旋转',
    description: '设置实体的旋转角度',
    icon: 'rotation',
    color: '#FF9800',
    tags: ['transform', 'rotation', 'set']
  });

  // 注册获取缩放节点
  registry.registerNodeType({
    type: 'transform/getScale',
    category: NodeCategory.ENTITY,
    constructor: GetScaleNode,
    label: '获取缩放',
    description: '获取实体的缩放比例',
    icon: 'scale',
    color: '#FF9800',
    tags: ['transform', 'scale', 'get']
  });

  // 注册设置缩放节点
  registry.registerNodeType({
    type: 'transform/setScale',
    category: NodeCategory.ENTITY,
    constructor: SetScaleNode,
    label: '设置缩放',
    description: '设置实体的缩放比例',
    icon: 'scale',
    color: '#FF9800',
    tags: ['transform', 'scale', 'set']
  });

  // 注册平移节点
  registry.registerNodeType({
    type: 'transform/translate',
    category: NodeCategory.ENTITY,
    constructor: TranslateNode,
    label: '平移',
    description: '平移实体位置',
    icon: 'move',
    color: '#FF9800',
    tags: ['transform', 'translate', 'move']
  });

  // 注册旋转节点
  registry.registerNodeType({
    type: 'transform/rotate',
    category: NodeCategory.ENTITY,
    constructor: RotateNode,
    label: '旋转',
    description: '旋转实体',
    icon: 'rotate',
    color: '#FF9800',
    tags: ['transform', 'rotate']
  });

  // 注册朝向目标节点
  registry.registerNodeType({
    type: 'transform/lookAt',
    category: NodeCategory.ENTITY,
    constructor: LookAtNode,
    label: '朝向目标',
    description: '让实体朝向指定目标',
    icon: 'target',
    color: '#FF9800',
    tags: ['transform', 'lookAt', 'target']
  });
}
